/**
 * Mobile Menu Component
 * Handles mobile navigation and search
 */

import React from 'react';
import { Link } from 'react-router-dom';
import { GenreResponse } from '../../interfaces/models/manga';
import SearchBar from './SearchBar';

interface MobileMenuProps {
  isLogin: boolean;
  genres: GenreResponse[];
  showGenresMobile: boolean;
  onToggleGenres: () => void;
  onMenuClose: () => void;
  onLogout: (e: React.MouseEvent) => void;
}

const MobileMenu = React.memo<MobileMenuProps>(({
  isLogin,
  genres,
  showGenresMobile,
  onToggleGenres,
  onMenuClose,
  onLogout
}) => {
  const scrollToTop = () => window.scrollTo(0, 0);

  const handleLinkClick = () => {
    scrollToTop();
    onMenuClose();
  };

  return (
    <div className="md:hidden fixed left-0 top-16 w-full bg-white text-gray-800 shadow-lg z-50 transition-all duration-200">
      {/* Mobile Search Bar */}
      <div className="px-4 py-3 border-b border-gray-200">
        <SearchBar
          isMobile={true}
          onResultClick={onMenuClose}
          placeholder="Tìm kiếm truyện..."
        />
      </div>

      {/* Navigation Links */}
      <div>
        <Link 
          to="/search" 
          onClick={handleLinkClick} 
          className="block px-6 py-3 text-base font-medium text-gray-700 hover:bg-gray-100 border-b border-gray-200 transition-colors"
        >
          <i className="fas fa-search mr-2"></i>
          Tìm kiếm nâng cao
        </Link>

        {/* Genres Section */}
        <button
          onClick={onToggleGenres}
          className="block w-full text-left px-6 py-3 text-base font-medium text-gray-700 hover:bg-gray-100 border-b border-gray-200 transition-colors"
        >
          <i className="fas fa-tags mr-2"></i>
          Thể loại
          <i className={`fas fa-chevron-${showGenresMobile ? 'up' : 'down'} ml-2 float-right mt-1`}></i>
        </button>

        {showGenresMobile && (
          <div className="bg-gray-50 py-2 border-b border-gray-200">
            <div className="grid grid-cols-2 gap-1">
              {genres.map((genre) => (
                <Link
                  key={genre.name}
                  to={`/genre/${genre.name}`}
                  className="block px-4 py-2 text-sm text-gray-700 hover:text-gray-900 hover:bg-gray-100 transition-colors truncate"
                  onClick={handleLinkClick}
                >
                  {genre.name}
                </Link>
              ))}
            </div>
          </div>
        )}

        {/* User-specific links */}
        {isLogin && (
          <>
            <Link 
              to="/profile/reading-history" 
              onClick={handleLinkClick} 
              className="block px-6 py-3 text-base font-medium text-gray-700 hover:bg-gray-100 border-b border-gray-200 transition-colors"
            >
              <i className="fas fa-history mr-2"></i>
              Lịch sử đọc
            </Link>
            <Link 
              to="/profile/favorites" 
              onClick={handleLinkClick} 
              className="block px-6 py-3 text-base font-medium text-gray-700 hover:bg-gray-100 border-b border-gray-200 transition-colors"
            >
              <i className="fas fa-heart mr-2"></i>
              Yêu thích
            </Link>
          </>
        )}
      </div>

      {/* Auth Section */}
      <div className="border-t border-gray-200">
        {isLogin ? (
          <>
            <Link 
              to="/profile" 
              onClick={handleLinkClick} 
              className="block px-6 py-3 text-base font-medium text-gray-700 hover:bg-gray-100 border-b border-gray-200 transition-colors"
            >
              <i className="fas fa-user mr-2"></i>
              Trang cá nhân
            </Link>
            <Link 
              to="/profile/settings" 
              onClick={handleLinkClick} 
              className="block px-6 py-3 text-base font-medium text-gray-700 hover:bg-gray-100 border-b border-gray-200 transition-colors"
            >
              <i className="fas fa-cog mr-2"></i>
              Cài đặt
            </Link>
            <a 
              href="#" 
              onClick={onLogout} 
              className="block px-6 py-3 text-base font-medium text-gray-700 hover:bg-gray-100 border-b border-gray-200 transition-colors"
            >
              <i className="fas fa-sign-out-alt mr-2"></i>
              Đăng xuất
            </a>
          </>
        ) : (
          <>
            <Link 
              to="/login" 
              onClick={handleLinkClick} 
              className="block px-6 py-3 text-base font-medium text-gray-700 hover:bg-gray-100 border-b border-gray-200 transition-colors"
            >
              <i className="fas fa-sign-in-alt mr-2"></i>
              Đăng nhập
            </Link>
            <Link 
              to="/register" 
              onClick={handleLinkClick} 
              className="block px-6 py-3 text-base font-medium text-gray-700 hover:bg-gray-100 border-b border-gray-200 transition-colors"
            >
              <i className="fas fa-user-plus mr-2"></i>
              Đăng ký
            </Link>
          </>
        )}
      </div>
    </div>
  );
});

MobileMenu.displayName = 'MobileMenu';

export default MobileMenu;
