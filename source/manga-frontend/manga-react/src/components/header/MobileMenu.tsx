/**
 * Mobile Menu Component
 * Handles mobile navigation and search
 */

import React from 'react';
import { Link } from 'react-router-dom';
import { GenreResponse } from '../../interfaces/models/manga';
import { useSearch } from '../../hooks/useSearch';
import SearchResults from './SearchResults';

interface MobileMenuProps {
  isLogin: boolean;
  genres: GenreResponse[];
  showGenresMobile: boolean;
  onToggleGenres: () => void;
  onMenuClose: () => void;
  onLogout: (e: React.MouseEvent) => void;
}

const MobileMenu = React.memo<MobileMenuProps>(({
  isLogin,
  genres,
  showGenresMobile,
  onToggleGenres,
  onMenuClose,
  onLogout
}) => {
  const scrollToTop = () => window.scrollTo(0, 0);

  const {
    searchKeyword,
    searchResults,
    isSearching,
    showResults,
    handleSearchInputChange,
    handleSearchSubmit,
    handleSearchFocus,
    hideResults
  } = useSearch();

  const handleLinkClick = () => {
    scrollToTop();
    onMenuClose();
  };

  const handleResultClick = () => {
    hideResults();
    scrollToTop();
    onMenuClose();
  };

  const handleSubmit = (e: React.FormEvent) => {
    const success = handleSearchSubmit(e);
    if (success) {
      hideResults();
      onMenuClose();
    }
  };

  return (
    <div className="md:hidden fixed left-0 top-16 w-full bg-white text-gray-800 shadow-lg z-50 transition-all duration-200 overflow-visible">
      {/* Mobile Search Bar */}
      <div className="px-4 py-3 border-b border-gray-200 relative overflow-visible">
        <div className="relative">
          <form onSubmit={handleSubmit} className="relative">
            <input
              type="text"
              value={searchKeyword}
              onChange={(e) => handleSearchInputChange(e.target.value)}
              onFocus={handleSearchFocus}
              placeholder="Tìm kiếm truyện..."
              className="w-full bg-gray-100 text-gray-800 rounded-full py-2 pl-4 pr-10 focus:outline-none focus:ring-2 focus:ring-purple-500 transition-all text-sm"
            />
            <button
              type="submit"
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
            >
              <i className="fas fa-search"></i>
            </button>
          </form>

          {/* Mobile search results dropdown */}
          {showResults && (
            <div className="absolute left-0 right-0 mt-1 bg-white rounded-md shadow-lg z-[9999] max-h-80 overflow-y-auto">
              <SearchResults
                isSearching={isSearching}
                searchResults={searchResults}
                searchKeyword={searchKeyword}
                onResultClick={handleResultClick}
              />
            </div>
          )}
        </div>
      </div>

      {/* Navigation Links */}
      <div>
        <Link
          to="/search"
          onClick={handleLinkClick}
          className="block px-6 py-3 text-base font-medium text-gray-700 hover:bg-gray-100 border-b border-gray-200 transition-colors"
        >
          <i className="fas fa-search mr-2"></i>
          Tìm kiếm nâng cao
        </Link>

        {/* Genres Section */}
        <button
          onClick={onToggleGenres}
          className="block w-full text-left px-6 py-3 text-base font-medium text-gray-700 hover:bg-gray-100 border-b border-gray-200 transition-colors"
        >
          <i className="fas fa-tags mr-2"></i>
          Thể loại
          <i className={`fas fa-chevron-${showGenresMobile ? 'up' : 'down'} ml-2 float-right mt-1`}></i>
        </button>

        {showGenresMobile && (
          <div className="bg-gray-50 py-2 border-b border-gray-200">
            <div className="grid grid-cols-2 gap-1">
              {genres.map((genre) => (
                <Link
                  key={genre.name}
                  to={`/genre/${genre.name}`}
                  className="block px-4 py-2 text-sm text-gray-700 hover:text-gray-900 hover:bg-gray-100 transition-colors truncate"
                  onClick={handleLinkClick}
                >
                  {genre.name}
                </Link>
              ))}
            </div>
          </div>
        )}

        {/* User-specific links */}
        {isLogin && (
          <>
            <Link
              to="/profile/reading-history"
              onClick={handleLinkClick}
              className="block px-6 py-3 text-base font-medium text-gray-700 hover:bg-gray-100 border-b border-gray-200 transition-colors"
            >
              <i className="fas fa-history mr-2"></i>
              Lịch sử đọc
            </Link>
            <Link
              to="/profile/favorites"
              onClick={handleLinkClick}
              className="block px-6 py-3 text-base font-medium text-gray-700 hover:bg-gray-100 border-b border-gray-200 transition-colors"
            >
              <i className="fas fa-heart mr-2"></i>
              Yêu thích
            </Link>
          </>
        )}
      </div>

      {/* Auth Section */}
      <div className="border-t border-gray-200">
        {isLogin ? (
          <>
            <Link
              to="/profile"
              onClick={handleLinkClick}
              className="block px-6 py-3 text-base font-medium text-gray-700 hover:bg-gray-100 border-b border-gray-200 transition-colors"
            >
              <i className="fas fa-user mr-2"></i>
              Trang cá nhân
            </Link>
            <Link
              to="/profile/settings"
              onClick={handleLinkClick}
              className="block px-6 py-3 text-base font-medium text-gray-700 hover:bg-gray-100 border-b border-gray-200 transition-colors"
            >
              <i className="fas fa-cog mr-2"></i>
              Cài đặt
            </Link>
            <a
              href="#"
              onClick={onLogout}
              className="block px-6 py-3 text-base font-medium text-gray-700 hover:bg-gray-100 border-b border-gray-200 transition-colors"
            >
              <i className="fas fa-sign-out-alt mr-2"></i>
              Đăng xuất
            </a>
          </>
        ) : (
          <>
            <Link
              to="/login"
              onClick={handleLinkClick}
              className="block px-6 py-3 text-base font-medium text-gray-700 hover:bg-gray-100 border-b border-gray-200 transition-colors"
            >
              <i className="fas fa-sign-in-alt mr-2"></i>
              Đăng nhập
            </Link>
            <Link
              to="/register"
              onClick={handleLinkClick}
              className="block px-6 py-3 text-base font-medium text-gray-700 hover:bg-gray-100 border-b border-gray-200 transition-colors"
            >
              <i className="fas fa-user-plus mr-2"></i>
              Đăng ký
            </Link>
          </>
        )}
      </div>
    </div>
  );
});

MobileMenu.displayName = 'MobileMenu';

export default MobileMenu;
