/**
 * Search Bar Component
 * Handles both desktop and mobile search functionality
 */

import React, { useRef, useEffect } from 'react';
import SearchResults from './SearchResults';
import { useSearch } from '../../hooks/useSearch';

interface SearchBarProps {
  isMobile?: boolean;
  onResultClick?: () => void;
  className?: string;
  placeholder?: string;
}

const SearchBar = React.memo<SearchBarProps>(({
  isMobile = false,
  onResultClick,
  className = '',
  placeholder = 'Tìm kiếm truyện...'
}) => {
  const searchInputRef = useRef<HTMLInputElement>(null);
  const searchResultsRef = useRef<HTMLDivElement>(null);

  const {
    searchKeyword,
    searchResults,
    isSearching,
    showResults,
    handleSearchInputChange,
    handleSearchSubmit,
    handleSearchFocus,
    hideResults,
    scrollToTop
  } = useSearch();

  // Handle outside click to close results
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        searchResultsRef.current &&
        !searchResultsRef.current.contains(event.target as Node) &&
        searchInputRef.current &&
        !searchInputRef.current.contains(event.target as Node)
      ) {
        hideResults();
      }
    };

    if (showResults) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [showResults, hideResults]);

  const handleResultClick = () => {
    hideResults();
    scrollToTop();
    onResultClick?.();
  };

  const handleSubmit = (e: React.FormEvent) => {
    const success = handleSearchSubmit(e);
    if (success) {
      hideResults();
      onResultClick?.();
    }
  };

  // Debug log for mobile
  if (process.env.NODE_ENV === 'development' && isMobile) {
    console.log('Mobile SearchBar:', {
      showResults,
      searchKeyword,
      searchResultsLength: searchResults.length,
      isSearching
    });
  }

  return (
    <div className={`relative ${className}`}>
      <form onSubmit={handleSubmit} className="relative">
        <input
          ref={searchInputRef}
          type="text"
          value={searchKeyword}
          onChange={(e) => handleSearchInputChange(e.target.value)}
          onFocus={handleSearchFocus}
          placeholder={placeholder}
          className={`w-full bg-gray-100 text-gray-900 rounded-full py-2 pl-5 pr-12 focus:outline-none focus:ring-2 focus:ring-purple-500 transition-all text-sm ${
            isMobile ? 'text-gray-800' : ''
          }`}
        />
        <button
          type="submit"
          className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-900 p-1"
        >
          <i className="fas fa-search"></i>
        </button>
      </form>

      {/* Search Results */}
      {showResults && (
        <div
          ref={searchResultsRef}
          className={`absolute top-full left-0 right-0 mt-1 z-[60] ${
            isMobile ? 'max-h-80 overflow-y-auto' : ''
          }`}
          style={isMobile ? {
            position: 'absolute',
            zIndex: 9999,
            backgroundColor: 'white',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
          } : {}}
        >
          <SearchResults
            isSearching={isSearching}
            searchResults={searchResults}
            searchKeyword={searchKeyword}
            onResultClick={handleResultClick}
          />
        </div>
      )}
    </div>
  );
});

SearchBar.displayName = 'SearchBar';

export default SearchBar;
