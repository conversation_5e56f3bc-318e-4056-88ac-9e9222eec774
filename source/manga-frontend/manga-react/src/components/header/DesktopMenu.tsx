/**
 * Desktop Menu Component
 * Handles desktop dropdown menu
 */

import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Hi<PERSON><PERSON>, <PERSON><PERSON>istory, <PERSON><PERSON><PERSON>t, <PERSON><PERSON>og, <PERSON><PERSON>ogout, Hi<PERSON>ogin, <PERSON><PERSON>serAdd } from 'react-icons/hi';

interface DesktopMenuProps {
  isLogin: boolean;
  onMenuClose: () => void;
  onLogout: (e: React.MouseEvent) => void;
}

const DesktopMenu = React.memo<DesktopMenuProps>(({
  isLogin,
  onMenuClose,
  onLogout
}) => {
  const scrollToTop = () => window.scrollTo(0, 0);

  const handleLinkClick = () => {
    scrollToTop();
    onMenuClose();
  };

  return (
    <div className="hidden md:block absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 py-1 z-50 transition-all duration-200">
      {isLogin ? (
        <>
          <Link
            to="/profile"
            onClick={handleLinkClick}
            className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition-colors"
          >
            <HiUser className="inline w-4 h-4 mr-2" />
            Trang cá nhân
          </Link>
          <Link
            to="/profile/reading-history"
            onClick={handleLinkClick}
            className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition-colors"
          >
            <HiHistory className="inline w-4 h-4 mr-2" />
            Lịch sử đọc
          </Link>
          <Link
            to="/profile/favorites"
            onClick={handleLinkClick}
            className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition-colors"
          >
            <HiHeart className="inline w-4 h-4 mr-2" />
            Yêu thích
          </Link>
          <Link
            to="/profile/settings"
            onClick={handleLinkClick}
            className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition-colors"
          >
            <HiCog className="inline w-4 h-4 mr-2" />
            Cài đặt
          </Link>
          <div className="border-t border-gray-100 my-1"></div>
          <a
            href="#"
            onClick={onLogout}
            className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition-colors"
          >
            <HiLogout className="inline w-4 h-4 mr-2" />
            Đăng xuất
          </a>
        </>
      ) : (
        <>
          <Link
            to="/login"
            onClick={handleLinkClick}
            className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition-colors"
          >
            <HiLogin className="inline w-4 h-4 mr-2" />
            Đăng nhập
          </Link>
          <Link
            to="/register"
            onClick={handleLinkClick}
            className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition-colors"
          >
            <HiUserAdd className="inline w-4 h-4 mr-2" />
            Đăng ký
          </Link>
        </>
      )}
    </div>
  );
});

DesktopMenu.displayName = 'DesktopMenu';

export default DesktopMenu;
