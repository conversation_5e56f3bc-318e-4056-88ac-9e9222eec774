/**
 * Desktop Menu Component
 * Handles desktop dropdown menu
 */

import React from 'react';
import { Link } from 'react-router-dom';

interface DesktopMenuProps {
  isLogin: boolean;
  onMenuClose: () => void;
  onLogout: (e: React.MouseEvent) => void;
}

const DesktopMenu = React.memo<DesktopMenuProps>(({
  isLogin,
  onMenuClose,
  onLogout
}) => {
  const scrollToTop = () => window.scrollTo(0, 0);

  const handleLinkClick = () => {
    scrollToTop();
    onMenuClose();
  };

  return (
    <div className="hidden md:block absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 py-1 z-50 transition-all duration-200">
      {isLogin ? (
        <>
          <Link 
            to="/profile" 
            onClick={handleLinkClick}
            className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition-colors"
          >
            <i className="fas fa-user mr-2"></i>
            Trang cá nhân
          </Link>
          <Link 
            to="/profile/reading-history" 
            onClick={handleLinkClick}
            className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition-colors"
          >
            <i className="fas fa-history mr-2"></i>
            Lịch sử đọc
          </Link>
          <Link 
            to="/profile/favorites" 
            onClick={handleLinkClick}
            className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition-colors"
          >
            <i className="fas fa-heart mr-2"></i>
            Yêu thích
          </Link>
          <Link 
            to="/profile/settings" 
            onClick={handleLinkClick}
            className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition-colors"
          >
            <i className="fas fa-cog mr-2"></i>
            Cài đặt
          </Link>
          <div className="border-t border-gray-100 my-1"></div>
          <a 
            href="#" 
            onClick={onLogout}
            className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition-colors"
          >
            <i className="fas fa-sign-out-alt mr-2"></i>
            Đăng xuất
          </a>
        </>
      ) : (
        <>
          <Link 
            to="/login" 
            onClick={handleLinkClick}
            className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition-colors"
          >
            <i className="fas fa-sign-in-alt mr-2"></i>
            Đăng nhập
          </Link>
          <Link 
            to="/register" 
            onClick={handleLinkClick}
            className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition-colors"
          >
            <i className="fas fa-user-plus mr-2"></i>
            Đăng ký
          </Link>
        </>
      )}
    </div>
  );
});

DesktopMenu.displayName = 'DesktopMenu';

export default DesktopMenu;
