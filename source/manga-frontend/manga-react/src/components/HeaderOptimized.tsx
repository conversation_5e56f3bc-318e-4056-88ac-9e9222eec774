/**
 * Optimized Header Component
 * Split into smaller components with custom hooks for better performance
 */

import React, { useCallback } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useMenu } from '../hooks/useMenu';
import { useGenres } from '../hooks/useGenres';
import { useScrollHeader } from '../hooks/useScrollHeader';
import { preventRapidClicks } from '../utils/performance';
import SearchBar from './header/SearchBar';
import MobileMenu from './header/MobileMenu';
import DesktopMenu from './header/DesktopMenu';

const HeaderOptimized = React.memo(() => {
  const { isLogin, logout, user } = useAuth();
  const { isScrolled } = useScrollHeader();
  const { genres } = useGenres();
  const {
    isMenuOpen,
    showGenresMobile,
    menuRef,
    toggleMenu,
    closeMenu,
    toggleGenresMobile
  } = useMenu();

  // Optimized logout handler with rapid click prevention
  const handleLogout = useCallback(
    preventRapidClicks(async (e: React.MouseEvent) => {
      e.preventDefault();
      try {
        await logout();
      } catch (error) {
        console.error('Header: Lỗi khi logout:', error);
      }
      closeMenu();
      window.scrollTo(0, 0);
    }),
    [logout, closeMenu]
  );

  return (
    <header className={`fixed top-0 left-0 w-full z-50 transition-all duration-300 ${
      isScrolled ? 'bg-white/95 backdrop-blur-md shadow-md' : 'bg-white'
    }`}>
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex-shrink-0">
            <Link to="/" className="flex items-center">
              <span className="text-gray-900 font-bold text-xl tracking-tight">
                R-Manga
              </span>
            </Link>
          </div>

          {/* Desktop Search Bar */}
          <div className="hidden md:flex flex-1 max-w-xl mx-auto">
            <SearchBar />
          </div>

          {/* Right side */}
          <div className="flex items-center space-x-4">
            {/* Username display when logged in (desktop only) */}
            {isLogin && user && (
              <div className="hidden md:flex items-center gap-2 px-3 py-1 rounded-lg text-gray-700 hover:text-gray-900 hover:bg-gray-100 transition-colors">
                <i className="fas fa-user"></i>
                <span className="text-sm font-medium truncate max-w-[120px]">
                  {user.displayName}
                </span>
              </div>
            )}

            {/* Menu Button */}
            <div className="relative" ref={menuRef}>
              <button
                onClick={toggleMenu}
                className="w-10 h-10 rounded-full text-gray-500 hover:text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-purple-500 transition-colors flex items-center justify-center"
                aria-label="User menu"
              >
                <i className="fas fa-bars"></i>
              </button>

              {/* Menu Dropdowns */}
              {isMenuOpen && (
                <>
                  {/* Mobile Menu */}
                  <MobileMenu
                    isLogin={isLogin}
                    genres={genres}
                    showGenresMobile={showGenresMobile}
                    onToggleGenres={toggleGenresMobile}
                    onMenuClose={closeMenu}
                    onLogout={handleLogout}
                  />

                  {/* Desktop Menu */}
                  <DesktopMenu
                    isLogin={isLogin}
                    onMenuClose={closeMenu}
                    onLogout={handleLogout}
                  />
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </header>
  );
});

HeaderOptimized.displayName = 'HeaderOptimized';

export default HeaderOptimized;
