/**
 * Header Comparison Component
 * For development testing - compare old vs optimized header
 */

import React, { useState } from 'react';
import NewHeader from './Header'; // Original header
import HeaderOptimized from './HeaderOptimized'; // Optimized header

const HeaderComparison: React.FC = () => {
  const [useOptimized, setUseOptimized] = useState(true);

  if (process.env.NODE_ENV !== 'development') {
    // In production, always use optimized version
    return <HeaderOptimized />;
  }

  return (
    <>
      {/* Development Toggle */}
      <div className="fixed top-20 right-4 z-[60] bg-black/80 text-white p-2 rounded text-xs">
        <label className="flex items-center gap-2">
          <input
            type="checkbox"
            checked={useOptimized}
            onChange={(e) => setUseOptimized(e.target.checked)}
          />
          Use Optimized Header
        </label>
      </div>

      {/* Render selected header */}
      {useOptimized ? <HeaderOptimized /> : <NewHeader />}
    </>
  );
};

export default HeaderComparison;
