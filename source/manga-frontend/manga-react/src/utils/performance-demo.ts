/**
 * Demo và test cases cho performance utilities
 * Chỉ dùng để development và testing
 */

import { 
  debounce, 
  throttle, 
  rafThrottle, 
  preventRapidClicks, 
  scrollThrottle, 
  resizeThrottle, 
  searchDebounce, 
  apiThrottle 
} from './performance';

// Demo functions để test
const demoApiCall = (query: string) => {
  console.log(`API call with query: ${query}`);
};

const demoScrollHandler = () => {
  console.log(`Scroll position: ${window.scrollY}`);
};

const demoResizeHandler = () => {
  console.log(`Window size: ${window.innerWidth}x${window.innerHeight}`);
};

const demoButtonClick = () => {
  console.log('Button clicked!');
};

// Test cases
export const performanceDemo = {
  // Test debounce
  testDebounce: () => {
    console.log('=== Testing Debounce ===');
    const debouncedApi = debounce(demoApiCall, 200);
    
    // Simulate rapid calls
    debouncedApi('test1');
    debouncedApi('test2');
    debouncedApi('test3');
    // Only 'test3' should be called after 200ms
  },

  // Test throttle
  testThrottle: () => {
    console.log('=== Testing Throttle ===');
    const throttledScroll = throttle(demoScrollHandler, 150);
    
    // Simulate rapid scroll events
    for (let i = 0; i < 10; i++) {
      setTimeout(() => throttledScroll(), i * 10);
    }
    // Should only call a few times, not 10 times
  },

  // Test search debounce
  testSearchDebounce: () => {
    console.log('=== Testing Search Debounce ===');
    const debouncedSearch = searchDebounce(demoApiCall);
    
    // Simulate typing
    debouncedSearch('n');
    debouncedSearch('na');
    debouncedSearch('nar');
    debouncedSearch('naru');
    debouncedSearch('naruto');
    // Only 'naruto' should be called after 200ms
  },

  // Test rapid click prevention
  testRapidClicks: () => {
    console.log('=== Testing Rapid Click Prevention ===');
    const protectedClick = preventRapidClicks(demoButtonClick, 100);
    
    // Simulate rapid clicks
    protectedClick();
    protectedClick();
    protectedClick();
    protectedClick();
    // Should only execute once
  },

  // Test scroll throttle
  testScrollThrottle: () => {
    console.log('=== Testing Scroll Throttle ===');
    const throttledScroll = scrollThrottle(demoScrollHandler);
    
    // Add event listener for testing
    window.addEventListener('scroll', throttledScroll, { passive: true });
    
    console.log('Scroll the page to see throttled output');
    
    // Remove after 5 seconds
    setTimeout(() => {
      window.removeEventListener('scroll', throttledScroll);
      console.log('Scroll throttle test ended');
    }, 5000);
  },

  // Test resize throttle
  testResizeThrottle: () => {
    console.log('=== Testing Resize Throttle ===');
    const throttledResize = resizeThrottle(demoResizeHandler);
    
    // Add event listener for testing
    window.addEventListener('resize', throttledResize);
    
    console.log('Resize the window to see throttled output');
    
    // Remove after 5 seconds
    setTimeout(() => {
      window.removeEventListener('resize', throttledResize);
      console.log('Resize throttle test ended');
    }, 5000);
  },

  // Test RAF throttle
  testRafThrottle: () => {
    console.log('=== Testing RAF Throttle ===');
    const rafThrottledHandler = rafThrottle(() => {
      console.log('RAF throttled function called');
    });
    
    // Simulate rapid calls
    for (let i = 0; i < 10; i++) {
      rafThrottledHandler();
    }
    // Should only call once per animation frame
  },

  // Test API throttle
  testApiThrottle: () => {
    console.log('=== Testing API Throttle ===');
    const throttledApi = apiThrottle(demoApiCall);
    
    // Simulate rapid API calls
    throttledApi('request1');
    throttledApi('request2');
    throttledApi('request3');
    // Should throttle the calls
  },

  // Run all tests
  runAllTests: () => {
    console.log('🚀 Running all performance utility tests...\n');
    
    performanceDemo.testDebounce();
    setTimeout(() => performanceDemo.testThrottle(), 1000);
    setTimeout(() => performanceDemo.testSearchDebounce(), 2000);
    setTimeout(() => performanceDemo.testRapidClicks(), 3000);
    setTimeout(() => performanceDemo.testRafThrottle(), 4000);
    setTimeout(() => performanceDemo.testApiThrottle(), 5000);
    
    console.log('\n✅ All tests scheduled. Check console for results.');
    console.log('📝 Note: Scroll and resize tests need manual interaction.');
  }
};

// Export for use in development
if (process.env.NODE_ENV === 'development') {
  // Make available globally for testing in browser console
  (window as any).performanceDemo = performanceDemo;
  console.log('🔧 Performance demo available at window.performanceDemo');
  console.log('💡 Try: performanceDemo.runAllTests()');
}
