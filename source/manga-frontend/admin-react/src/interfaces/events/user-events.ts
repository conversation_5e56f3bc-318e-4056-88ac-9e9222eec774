/**
 * Kafka Event Schemas for User Management
 * These interfaces define the structure of events sent to Kafka
 */

// Base event interface
export interface BaseEvent {
  eventId: string;
  eventType: string;
  timestamp: string;
  source: string;
  version: string;
}

// User status changed event
export interface UserStatusChangedEvent extends BaseEvent {
  eventType: 'USER_STATUS_CHANGED';
  data: {
    userId: string;
    username: string;
    email: string;
    previousStatus: boolean;
    newStatus: boolean;
    reason?: string; // Lý do khóa (chỉ có khi newStatus = false)
    adminId: string;
    adminUsername: string;
    actionTimestamp: string;
  };
}

// Email notification event (sẽ được consume bởi notification service)
export interface EmailNotificationEvent extends BaseEvent {
  eventType: 'EMAIL_NOTIFICATION_REQUESTED';
  data: {
    to: string;
    subject: string;
    template: string;
    templateData: {
      username: string;
      reason?: string;
      adminUsername: string;
      actionDate: string;
      supportEmail: string;
      websiteUrl: string;
    };
    priority: 'HIGH' | 'MEDIUM' | 'LOW';
    category: 'ACCOUNT_SECURITY' | 'SYSTEM_NOTIFICATION' | 'MARKETING';
  };
}

// Audit log event
export interface AuditLogEvent extends BaseEvent {
  eventType: 'ADMIN_ACTION_LOGGED';
  data: {
    adminId: string;
    adminUsername: string;
    action: 'BLOCK_USER' | 'UNBLOCK_USER' | 'DELETE_USER' | 'UPDATE_USER';
    targetUserId: string;
    targetUsername: string;
    details: {
      reason?: string;
      previousValues?: Record<string, any>;
      newValues?: Record<string, any>;
    };
    ipAddress: string;
    userAgent: string;
    timestamp: string;
  };
}

// Kafka topic names
export const KAFKA_TOPICS = {
  USER_STATUS_CHANGED: 'user.status.changed',
  EMAIL_NOTIFICATION: 'notification.email.requested',
  AUDIT_LOG: 'admin.action.logged'
} as const;

// Email templates
export const EMAIL_TEMPLATES = {
  ACCOUNT_BLOCKED: 'account-blocked',
  ACCOUNT_UNBLOCKED: 'account-unblocked',
  ACCOUNT_DELETED: 'account-deleted'
} as const;
