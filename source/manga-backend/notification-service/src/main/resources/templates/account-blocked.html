<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>n của bạn đã bị tạm kh<PERSON>a</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f4f4f4;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        
        .header {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
        }
        
        .logo {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 10px;
            letter-spacing: 2px;
        }
        
        .header-subtitle {
            font-size: 16px;
            opacity: 0.9;
        }
        
        .alert-icon {
            font-size: 48px;
            margin-bottom: 15px;
        }
        
        .content {
            padding: 40px 30px;
        }
        
        .alert-section {
            text-align: center;
            margin-bottom: 30px;
            padding: 25px;
            background-color: #fdf2f2;
            border: 2px solid #fecaca;
            border-radius: 12px;
        }
        
        .alert-title {
            font-size: 24px;
            color: #dc2626;
            margin-bottom: 15px;
            font-weight: 600;
        }
        
        .username {
            color: #dc2626;
            font-weight: bold;
        }
        
        .alert-message {
            color: #7f1d1d;
            font-size: 16px;
            margin-bottom: 20px;
        }
        
        .block-details {
            background-color: #fff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 25px;
            margin: 25px 0;
        }
        
        .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f3f4f6;
        }
        
        .detail-row:last-child {
            border-bottom: none;
        }
        
        .detail-label {
            font-weight: 600;
            color: #374151;
            min-width: 120px;
        }
        
        .detail-value {
            color: #6b7280;
            text-align: right;
            flex: 1;
        }
        
        .reason-highlight {
            background-color: #fef3c7;
            color: #92400e;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #f59e0b;
            margin: 20px 0;
        }
        
        .reason-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: #78350f;
        }
        
        .reason-text {
            font-style: italic;
        }
        
        .info-section {
            background-color: #eff6ff;
            border: 1px solid #bfdbfe;
            border-radius: 8px;
            padding: 20px;
            margin: 25px 0;
        }
        
        .info-title {
            font-weight: 600;
            color: #1e40af;
            margin-bottom: 10px;
        }
        
        .info-text {
            color: #1e3a8a;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .contact-section {
            text-align: center;
            margin: 30px 0;
            padding: 25px;
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            border-radius: 12px;
            color: white;
        }
        
        .contact-title {
            font-size: 18px;
            margin-bottom: 15px;
            font-weight: 600;
        }
        
        .contact-button {
            display: inline-block;
            background-color: white;
            color: #1d4ed8;
            padding: 12px 25px;
            text-decoration: none;
            border-radius: 25px;
            font-weight: bold;
            margin-top: 10px;
            transition: transform 0.3s ease;
        }
        
        .contact-button:hover {
            transform: translateY(-2px);
        }
        
        .footer {
            background-color: #2c3e50;
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .footer-text {
            font-size: 12px;
            color: #95a5a6;
            margin-top: 15px;
        }
        
        .footer-links {
            margin-bottom: 20px;
        }
        
        .footer-link {
            color: #ecf0f1;
            text-decoration: none;
            margin: 0 15px;
            font-size: 14px;
        }
        
        .footer-link:hover {
            color: #3498db;
        }
        
        @media (max-width: 600px) {
            .container {
                margin: 0;
                box-shadow: none;
            }
            
            .header, .content, .footer {
                padding: 20px;
            }
            
            .alert-title {
                font-size: 20px;
            }
            
            .detail-row {
                flex-direction: column;
                align-items: flex-start;
                gap: 5px;
            }
            
            .detail-value {
                text-align: left;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="alert-icon">🚫</div>
            <div class="logo">R-MANGA</div>
            <div class="header-subtitle">Thông báo bảo mật tài khoản</div>
        </div>
        
        <!-- Content -->
        <div class="content">
            <!-- Alert Section -->
            <div class="alert-section">
                <h1 class="alert-title">Tài khoản đã bị tạm khóa</h1>
                <p class="alert-message">
                    Xin chào <span class="username">{{username}}</span>,<br>
                    Tài khoản của bạn trên R-Manga đã bị tạm khóa do vi phạm quy định sử dụng.
                </p>
            </div>
            
            <!-- Block Details -->
            <div class="block-details">
                <div class="detail-row">
                    <span class="detail-label">Tài khoản:</span>
                    <span class="detail-value">{{username}}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Thời gian:</span>
                    <span class="detail-value">{{actionDate}}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Trạng thái:</span>
                    <span class="detail-value" style="color: #dc2626; font-weight: 600;">Đã khóa</span>
                </div>
            </div>
            
            <!-- Reason -->
            <div class="reason-highlight">
                <div class="reason-title">Lý do khóa tài khoản:</div>
                <div class="reason-text">"{{reason}}"</div>
            </div>
            
            <!-- Info Section -->
            <div class="info-section">
                <div class="info-title">📋 Thông tin quan trọng</div>
                <div class="info-text">
                    • Trong thời gian bị khóa, bạn không thể đăng nhập vào tài khoản<br>
                    • Tất cả dữ liệu của bạn vẫn được bảo toàn<br>
                    • Thời gian khóa có thể thay đổi tùy thuộc vào mức độ vi phạm<br>
                    • Bạn có thể khiếu nại nếu cho rằng đây là nhầm lẫn
                </div>
            </div>
            
            <!-- Contact Section -->
            <div class="contact-section">
                <div class="contact-title">Cần hỗ trợ?</div>
                <p>Nếu bạn cho rằng đây là nhầm lẫn hoặc cần làm rõ thêm, vui lòng liên hệ với chúng tôi.</p>
                <a href="mailto:{{supportEmail}}" class="contact-button">Liên hệ hỗ trợ</a>
            </div>
        </div>
        
        <!-- Footer -->
        <div class="footer">
            <div class="footer-links">
                <a href="{{websiteUrl}}" class="footer-link">Trang chủ</a>
                <a href="{{websiteUrl}}/terms" class="footer-link">Điều khoản</a>
                <a href="{{websiteUrl}}/privacy" class="footer-link">Bảo mật</a>
            </div>
            <div class="footer-text">
                © 2024 R-Manga. Tất cả quyền được bảo lưu.<br>
                Email này được gửi đến {{email}}
            </div>
        </div>
    </div>
</body>
</html>
