<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chào mừng đến với R-<PERSON>ga</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f4f4f4;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
        }
        
        .logo {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 10px;
            letter-spacing: 2px;
        }
        
        .header-subtitle {
            font-size: 16px;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px 30px;
        }
        
        .welcome-message {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .welcome-title {
            font-size: 28px;
            color: #333;
            margin-bottom: 15px;
            font-weight: 600;
        }
        
        .welcome-subtitle {
            font-size: 16px;
            color: #666;
            margin-bottom: 30px;
        }
        
        .username {
            color: #667eea;
            font-weight: bold;
        }
        
        .features {
            margin: 30px 0;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9ff;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        
        .feature-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            color: white;
            font-weight: bold;
        }
        
        .feature-text {
            flex: 1;
        }
        
        .feature-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }
        
        .feature-desc {
            color: #666;
            font-size: 14px;
        }
        
        .cta-section {
            text-align: center;
            margin: 40px 0;
            padding: 30px;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            border-radius: 12px;
            color: white;
        }
        
        .cta-button {
            display: inline-block;
            background-color: white;
            color: #f5576c;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 25px;
            font-weight: bold;
            margin-top: 15px;
            transition: transform 0.3s ease;
        }
        
        .cta-button:hover {
            transform: translateY(-2px);
        }
        
        .footer {
            background-color: #2c3e50;
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .footer-links {
            margin-bottom: 20px;
        }
        
        .footer-link {
            color: #ecf0f1;
            text-decoration: none;
            margin: 0 15px;
            font-size: 14px;
        }
        
        .footer-link:hover {
            color: #3498db;
        }
        
        .footer-text {
            font-size: 12px;
            color: #95a5a6;
            margin-top: 15px;
        }
        
        @media (max-width: 600px) {
            .container {
                margin: 0;
                box-shadow: none;
            }
            
            .header, .content, .footer {
                padding: 20px;
            }
            
            .welcome-title {
                font-size: 24px;
            }
            
            .feature-item {
                flex-direction: column;
                text-align: center;
            }
            
            .feature-icon {
                margin-right: 0;
                margin-bottom: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="logo">R-MANGA</div>
            <div class="header-subtitle">Thế giới manga tuyệt vời</div>
        </div>
        
        <!-- Content -->
        <div class="content">
            <div class="welcome-message">
                <h1 class="welcome-title">Chào mừng <span class="username">{{username}}</span>!</h1>
                <p class="welcome-subtitle">
                    Cảm ơn bạn đã tham gia cộng đồng R-Manga. Hành trình khám phá những câu chuyện tuyệt vời bắt đầu từ đây!
                </p>
            </div>
            
            <!-- Features -->
            <div class="features">
                <div class="feature-item">
                    <div class="feature-icon">📚</div>
                    <div class="feature-text">
                        <div class="feature-title">Thư viện khổng lồ</div>
                        <div class="feature-desc">Hàng nghìn bộ manga từ nhiều thể loại khác nhau</div>
                    </div>
                </div>
                
                <div class="feature-item">
                    <div class="feature-icon">💖</div>
                    <div class="feature-text">
                        <div class="feature-title">Yêu thích & Theo dõi</div>
                        <div class="feature-desc">Lưu những bộ manga yêu thích và nhận thông báo cập nhật</div>
                    </div>
                </div>
                
                <div class="feature-item">
                    <div class="feature-icon">💬</div>
                    <div class="feature-text">
                        <div class="feature-title">Cộng đồng sôi động</div>
                        <div class="feature-desc">Thảo luận và chia sẻ cảm nhận với những người yêu manga</div>
                    </div>
                </div>
                
                <div class="feature-item">
                    <div class="feature-icon">📱</div>
                    <div class="feature-text">
                        <div class="feature-title">Đọc mọi lúc mọi nơi</div>
                        <div class="feature-desc">Giao diện responsive, tối ưu cho mọi thiết bị</div>
                    </div>
                </div>
            </div>
            
            <!-- CTA Section -->
            <div class="cta-section">
                <h2>Sẵn sàng khám phá?</h2>
                <p>Hãy bắt đầu hành trình manga của bạn ngay hôm nay!</p>
                <a href="{{websiteUrl}}" class="cta-button">Khám phá ngay</a>
            </div>
        </div>
        
        <!-- Footer -->
        <div class="footer">
            <div class="footer-links">
                <a href="{{websiteUrl}}" class="footer-link">Trang chủ</a>
                <a href="{{websiteUrl}}/help" class="footer-link">Hỗ trợ</a>
                <a href="{{websiteUrl}}/contact" class="footer-link">Liên hệ</a>
            </div>
            <div class="footer-text">
                © 2024 R-Manga. Tất cả quyền được bảo lưu.<br>
                Email này được gửi đến {{email}}
            </div>
        </div>
    </div>
</body>
</html>
